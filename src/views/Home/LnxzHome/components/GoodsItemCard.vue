<template>
  <div class="goods-list-container" :style="containerStyle">
    <div class="goods-list-header">
      <img v-if="icon" class="header-icon" :src="icon" alt="icon">
      <div class="header-title">{{ title }}</div>
      <div v-if="desc" class="header-desc">{{ desc }}</div>
      <div v-if="more" class="header-more" @click="onMoreClick">{{ more }}</div>
    </div>
    <div class="goods-scroll-container">
      <div class="goods-list">
        <div class="goods-item" v-for="(item, index) in goodsList" :key="index" @click="onGoodsClick(item)">
          <div class="goods-img-container">
            <img class="goods-img" v-lazy="item.listImageUrl" :alt="item.skuList[0].name">
          </div>
          <div class="goods-info">
            <div class="goods-name">{{ item.skuList[0].name }}</div>
            <div class="goods-sold" v-if="item.skuList[0].realSaleVolume">已售{{ item.skuList[0].realSaleVolume }}件</div>
          </div>
          <div class="goods-price"><span>¥</span>{{ (item.skuList[0].price / 100).toFixed(2) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 定义props
const props = defineProps({
  goodsList: {
    type: Array,
    default: () => []
  },
  icon: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: '商品列表'
  },
  desc: {
    type: String,
    default: ''
  },
  more: {
    type: String,
    default: ''
  },
  containerStyle: {
    type: String,
    default: ''
  }
})

// 定义emits
const emit = defineEmits(['moreClick', 'goodsClick'])

// 事件处理方法
const onMoreClick = () => {
  emit('moreClick')
}

const onGoodsClick = (item) => {
  emit('goodsClick', item)
}
</script>

<style lang="less" scoped>
.goods-list-container {
  margin: 10px;
  padding: 15px 10px;
  border: 1px solid #fff;
  box-shadow: 0px 7px 10px 0px rgba(0, 0, 0, 0.01);
  border-radius: 15px;

  .goods-list-header {
    display: flex;
    align-items: flex-end;
    margin-bottom: 12px;

    .header-icon {
      margin-right: 8px;
      width: 25px;
      height: 25px;
      transform: translateY(1px);
    }

    .header-title {
      margin-right: 8px;
      line-height: 20px;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .header-desc {
      line-height: 20px;
      font-size: 12px;
      color: #979797;
      font-weight: 400;
    }

    .header-more {
      position: relative;
      margin-left: auto;
      padding-right: 16px;
      line-height: 20px;
      font-size: 12px;
      color: #979797;
      font-weight: 400;

      &::after {
        content: '';
        position: absolute;
        right: 4px;
        top: 48%;
        transform: translateY(-50%);
        width: 6px;
        height: 10px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='6' height='10' viewBox='0 0 6 10' fill='none'%3E%3Cpath d='M1 1L5 5L1 9' stroke='%23999' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat;
        background-size: contain;
      }
    }
  }

  .goods-scroll-container {
    overflow: hidden;
    overflow-x: scroll;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .goods-list {
    display: flex;
    min-width: 100%;

    .goods-item {
      flex: 0 0 calc(33.33% - 8px); // 计算方式：(100% / 3) - ((12px+12px) / 3))
      margin-right: 12px;
      display: flex;
      flex-direction: column;

      &:last-child {
        margin-right: 0;
      }

      .goods-img-container {
        margin-bottom: 8px;
        border-radius: 8px;
        overflow: hidden;
        font-size: 0;
      }

      .goods-img {
        width: 100%;
        aspect-ratio: 1/1;
        object-fit: cover;
      }

      .goods-info {
        height: 46px;
      }

      .goods-name {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-bottom: 4px;
        line-height: 18px;
        font-size: 14px;
        color: #333;
        font-weight: 400;
      }

      .goods-sold {
        font-size: 12px;
        color: #B1B1B1;
        font-weight: 400;
        transform: scale(0.9);
        transform-origin: left;
      }

      .goods-price {
        font-family: OPPOSans-B;
        font-size: 16px;
        color: #DD2B2B;
        font-weight: 400;

        span {
          margin-right: 2px;
          font-size: 18px;
        }
      }
    }
  }
}
</style>
